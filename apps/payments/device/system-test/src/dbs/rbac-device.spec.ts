import { retry, sleep } from '@npco/bff-systemtest-utils';

import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { describeIf } from '../testIf';

import { DbsApiTestHelper } from './dbsApiTestHelper';

const { STAGE: stage } = process.env;
describeIf(stage === 'dev', 'role based access control system tests', () => {
  const apiTestHelper = new DbsApiTestHelper();

  const updateDeviceSettings = async (settings: any) =>
    (await apiTestHelper.getOpenIdClient()).mutate({
      mutation: gql`
        mutation updateDeviceSettings($settings: UpdateDeviceSettingsInput!) {
          updateDeviceSettings(deviceSettings: $settings)
        }
      `,
      variables: {
        settings,
      },
    });

  const siteUuid = apiTestHelper.getSiteUuid();
  const assignedDevice = apiTestHelper.getDeviceUuid();
  console.log('assignedDeviceId', assignedDevice);

  const queryMaterializedView = async () => {
    const res: any = await apiTestHelper.dbClient.query({
      TableName: apiTestHelper.getComponentTableName(),
      KeyConditionExpression: 'id = :id AND begins_with(#type, :type)',
      ExpressionAttributeValues: {
        ':type': 'customer.entity',
        ':id': apiTestHelper.getCustomerUuid(),
      },
      ExpressionAttributeNames: {
        '#type': 'type',
      },
    });
    return res.Items?.[0];
  };

  let unassignedDevice: string;
  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await apiTestHelper.sendDeviceProjectionEvent('dbs.Device.Updated', {
      siteUuid: apiTestHelper.getSiteUuid(),
      entityUuid: apiTestHelper.getEntityUuid(),
      name: 'zeller device name 1',
      site: {
        name: 'zeller site name',
        pin: '1203',
        siteUuid,
        refundRequiresPin: true,
      },
      model: uuidv4(),
      serial: uuidv4(),
      status: 'ACTIVE',
      deviceUuid: assignedDevice,
    });
    await retry(async () => {
      const device = await apiTestHelper.api.getDeviceSettings(assignedDevice);
      expect(device.getDeviceSettings.id).toBeDefined();
    });
    const site2 = uuidv4();
    const model = uuidv4();
    const serial = uuidv4();
    unassignedDevice = uuidv4();
    await apiTestHelper.sendDeviceProjectionEvent('dbs.Device.Updated', {
      siteUuid: site2,
      entityUuid: apiTestHelper.getEntityUuid(),
      name: 'zeller device name 2',
      site: {
        name: 'zeller site name',
        pin: '1203',
        siteUuid: site2,
        refundRequiresPin: true,
      },
      model,
      serial,
      status: 'ACTIVE',
      deviceUuid: unassignedDevice,
    });
    await retry(async () => {
      const device = await apiTestHelper.api.getDeviceSettings(unassignedDevice);
      expect(device.getDeviceSettings.id).toBeDefined();
    });

    await apiTestHelper.updateCustomer({
      customerUuid: apiTestHelper.getCustomerUuid(),
      entityUuid: apiTestHelper.getEntityUuid(),
      sites: [apiTestHelper.getSiteUuid()],
    });
    await retry(async () => {
      expect((await queryMaterializedView()).sites).toEqual([apiTestHelper.getSiteUuid()]);
    });
    await retry(async () => {
      await apiTestHelper.setTestCustomerAsRole('MANAGER');
    });
  });

  describe('device manager restricted tests', () => {
    it('should update device if assigned to device site', async () =>
      retry(async () => {
        await updateDeviceSettings({ id: assignedDevice, name: 'new name' })
          .then(() => expect('fail').toBe('shouldNotGetHere'))
          .catch((e) => {
            console.log(e.message);
            // Expect error from ams api
            expect(e.message.includes("[400] Devices's id") || e.message.includes('Failed to update device')).toBe(
              true,
            );
          });
      }));

    it('should fail to query device if not assigned to device site', async () =>
      retry(async () => {
        console.log('unassignedDevice', unassignedDevice);
        await updateDeviceSettings({ id: unassignedDevice, name: 'new name' })
          .then(() => expect('fail').toBe('shouldNotGetHere'))
          // expect error not allowed from dbs api
          .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
      }));

    xit('should to subscribe for deviceSettings update for assigned device/site', () => {
      apiTestHelper.api
        .deviceSettingUpdateSubscriber(unassignedDevice, { siteUuid }, (result: any, subscribe: any) => {
          subscribe.unsubscribe();
        })
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e: any) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });
    xit('should fail to subscribe for deviceSettings update for unassigned manager', () => {
      apiTestHelper.api
        .deviceSettingUpdateSubscriber(unassignedDevice, { siteUuid }, (result: any, subscribe: any) => {
          subscribe.unsubscribe();
        })
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e: any) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });
  });
});
