/* eslint import/no-import-module-exports: 0 */
/* eslint no-template-curly-in-string: 0 */
/* eslint no-useless-concat: 0 */
// eslint-disable-next-line import/no-extraneous-dependencies
import * as dotenv from 'dotenv';
import type { Serverless } from 'serverless/aws';

import { EnvService } from './serverless/envService';
import { golangExecutable, packagePatterns, providedAl2023 } from './serverless/globals';
import { getDeploymentBucket } from './serverless/shared/deploymentBucket';
import { getTags } from './serverless/shared/getTags';
import { getVpc } from './serverless/shared/getVpc';
import { getEnvFilename } from './serverless/utils/getEnvFilename';

dotenv.config({ path: getEnvFilename(), debug: true });
const env = new EnvService();
const region = '${opt:region}';
const stage = '${opt:stage}';

const serviceBaseName = `\${opt:stage}-${env.COMPONENT_NAME}-${env.PART_NAME}` as const;
const serviceName = `${serviceBaseName}-api` as const;
const stackName = serviceName;

const devVpn = { 'Fn::If': ['ShouldAllowVpn', 'vpce-0d16bbe7c101e4492', { Ref: 'AWS::NoValue' }] };

const generateServerless = async (): Promise<Serverless> => {
  return {
    service: serviceName,
    plugins: ['serverless-plugin-resource-tagging', 'serverless-plugin-tracing', 'serverless-plugin-scripts'],
    useDotenv: true,
    variablesResolutionMode: '20210326',
    frameworkVersion: '3',
    provider: {
      name: 'aws',
      runtime: providedAl2023,
      apiName: serviceName,
      region,
      stackName,
      vpc: await getVpc(env),
      tracing: {
        lambda: true,
        apiGateway: true,
      },
      versionFunctions: false,
      deploymentBucket: await getDeploymentBucket(env),
      environment: {
        COMPONENT_NAME: env.COMPONENT_NAME,
        PART_NAME: env.PART_NAME,
        STAGE: stage,
        LOG_LEVEL: env.LOG_LEVEL,
        COMPONENT_TABLE: env.COMPONENT_TABLE,
        ZELLER_DEVID: env.ZELLER_DEVID,
        TEVALIS_API_URL: env.TEVALIS_API_URL,
      },
      tags: getTags(env),
      stackTags: getTags(env),

      endpointType: '${self:custom.endpointTypes.${opt:stage}, self:custom.endpointTypes.other}',
      vpcEndpointIds: '${self:custom.vpcEndpointIds.${opt:stage}, null}',
      apiGateway: {
        metrics: true,
        resourcePolicy: [
          '${self:custom.resourcePolicy.${opt:stage}, self:custom.resourcePolicy.other}',
          {
            Effect: 'Allow',
            Principal: { AWS: '*' },
            Action: 'execute-api:Invoke',
            Resource: 'execute-api:/*/*/*',
          },
        ],
      },
    },
    package: packagePatterns,
    custom: {
      apiVersion: 'v1',
      endpointTypes: {
        dev: 'private',
        staging: 'private',
        prod: 'private',
        other: 'regional',
      },
      vpcEndpointIds: {
        dev: ['${ssm:/${env:STATIC_ENV_NAME}-vpc-shared-01-api-gateway-endpoint}', devVpn],
        staging: ['${ssm:/${env:STATIC_ENV_NAME}-vpc-shared-01-api-gateway-endpoint}'],
        prod: ['${ssm:/${env:STATIC_ENV_NAME}-vpc-shared-01-api-gateway-endpoint}'],
      },
      resourcePolicy: {
        dev: {
          Effect: 'Deny',
          Principal: { AWS: '*' },
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
          Condition: {
            StringNotEquals: { 'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}' },
          },
        },
        staging: {
          Effect: 'Deny',
          Principal: { AWS: '*' },
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
          Condition: {
            StringNotEquals: { 'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}' },
          },
        },
        prod: {
          Effect: 'Deny',
          Principal: { AWS: '*' },
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
          Condition: {
            StringNotEquals: { 'aws:sourceVpce': '${self:custom.vpcEndpointIds.${opt:stage}, null}' },
          },
        },
        other: {
          Effect: 'Allow',
          Principal: { AWS: '*' },
          Action: 'execute-api:Invoke',
          Resource: 'execute-api:/*/*/*',
        },
      },
    },
    functions: {
      cronJobHandler: {
        handler: golangExecutable,
        name: `${stackName}-cron`,
        timeout: 300,
        tracing: true,
        role: 'cronJobRole',
        environment: {
          HANDLER_NAME: 'cronjob',
        },
        events: [
          {
            schedule: {
              rate: 'rate(1 minute)',
              enabled: false,
              input: '{"cronJobType": "tevalis"}',
            },
          },
        ],
      },
      apiHandler: {
        handler: golangExecutable,
        name: `${stackName}-api-handler`,
        timeout: 30,
        tracing: true,
        role: 'apiHandlerRole',
        environment: {
          HANDLER_NAME: 'api-handler',
        },
        events: [
          {
            http: { path: '${self:custom.apiVersion}/payment/{siteUuid}', method: 'post' },
          },
          {
            http: { path: '${self:custom.apiVersion}/order/{siteUuid}/{orderId}', method: 'get' },
          },
          {
            http: { path: '${self:custom.apiVersion}/orders/{siteUuid}', method: 'get' },
          },
          {
            http: { path: '${self:custom.apiVersion}/health', method: 'get' },
          },
        ],
      },
    },
    resources: {
      Resources: {
        logPolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: `${stackName}-logPolicy`,
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
                  Resource: 'arn:aws:logs:${opt:region}:${aws:accountId}:log-group:/aws/lambda/' + `${stackName}-*:*`,
                },
              ],
            },
          },
        },
        xrayPolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: '${self:provider.stackName}-xrayPolicy',
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: [
                    'xray:PutTraceSegments',
                    'xray:PutTelemetryRecords',
                    'xray:GetSamplingRules',
                    'xray:GetSamplingTargets',
                    'xray:GetSamplingStatisticSummaries',
                  ],
                  Resource: '*',
                },
              ],
            },
          },
        },
        lambdaVpcPolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: '${self:provider.stackName}-lambdaVpcPolicy',
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
                  Resource: '*',
                },
              ],
            },
          },
        },
        cpiTablePolicy: {
          Type: 'AWS::IAM::ManagedPolicy',
          Properties: {
            ManagedPolicyName: '${self:provider.stackName}-cpiTablePolicy',
            PolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Action: ['dynamodb:Query'],
                  Resource: 'arn:aws:dynamodb:${opt:region}:${aws:accountId}:table/${env:STATIC_ENV_NAME}-cpi-engine-dynamodb-Entities*',
                },
              ],
            },
          },
        },
        cronJobRole: {
          Type: 'AWS::IAM::Role',
          Properties: {
            AssumeRolePolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Principal: {
                    Service: ['lambda.amazonaws.com'],
                  },
                  Action: 'sts:AssumeRole',
                },
              ],
            },
            ManagedPolicyArns: [
              {
                Ref: 'lambdaVpcPolicy',
              },
              {
                Ref: 'xrayPolicy',
              },
              {
                Ref: 'logPolicy',
              },
            ],
          },
        },
        apiHandlerRole: {
          Type: 'AWS::IAM::Role',
          Properties: {
            AssumeRolePolicyDocument: {
              Version: '2012-10-17',
              Statement: [
                {
                  Effect: 'Allow',
                  Principal: {
                    Service: ['lambda.amazonaws.com'],
                  },
                  Action: 'sts:AssumeRole',
                },
              ],
            },
            ManagedPolicyArns: [
              {
                Ref: 'cpiTablePolicy',
              },
              {
                Ref: 'lambdaVpcPolicy',
              },
              {
                Ref: 'xrayPolicy',
              },
              {
                Ref: 'logPolicy',
              },
            ],
          },
        },
        apiGatewayEndpoint: {
          Type: 'AWS::SSM::Parameter',
          Properties: {
            Name: `${serviceName}-rest-endpoint`,
            Description: 'API Gateway Endpoint',
            Type: 'String',
            Value: {
              'Fn::Join': [
                '',
                [
                  'https://',
                  {
                    Ref: 'ApiGatewayRestApi',
                  },
                  '.execute-api.${self:provider.region}.amazonaws.com/',
                  '${opt:stage}',
                  '/${self:custom.apiVersion}',
                ],
              ],
            },
          },
        }
      },
      Conditions: {
        ShouldAllowVpn: {
          'Fn::And': [
            {
              'Fn::Equals': ['${self:provider.region}', '${env:PRIMARY_REGION}'],
            },
            {
              'Fn::Equals': ['${env:VPC_ENV_NAME}', 'dev'],
            },
          ],
        },
      },
    },
  } as any;
};

module.exports = generateServerless();
