package app

import (
	"context"
	"fmt"
	"os"
	"runtime"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/middleware"
	l "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/lambdas"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type HandlerName string

const (
	ApiHandler string = "api-handler"
	Warmup     string = "warmup"
)

// Launch initializes the application components and starts the lambda handler.
func Launch() {
	lambdaEnv := os.Getenv("AWS_LAMBDA_RUNTIME_API")
	handlerName := os.Getenv("HANDLER_NAME")

	var ctx = context.Background()
	logger.Debug(ctx, "Get handler name:"+handlerName+lambdaEnv)
	ctx = logger.AddMetadata(ctx, "provider", types.TEVALIS)
	logger.Debug(ctx, fmt.Sprintf("Go version: %s\n", runtime.Version()))

	lambdas := l.New(ctx)

	switch handlerName {
	case ApiHandler:
		lambda.Start(middleware.BaseMiddlewares(middleware.APIGatewayProxyMiddleware(lambdas.ApiHandler)))
	case Warmup:
		lambda.Start(lambdas.WarmupHandler)
	}
}
