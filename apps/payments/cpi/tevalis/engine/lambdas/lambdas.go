package lambdas

import (
	"context"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/warmup"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/handlers/api"
)

type Lambda struct {
	apiHandler *api.ApiHandler
	// warmupHandler *warmup.WarmupHandler
}

func New(ctx context.Context) *Lambda {
	return &Lambda{api.NewHandler(ctx)}
}

func (l *Lambda) ApiHandler(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	result, _ := l.apiHandler.HandleApiRequest(ctx, event)
	// If an error is returned, http gateway will return 500 and not the correct error code or message
	return result, nil
}

func (l *Lambda) WarmupHandler(ctx context.Context) {
	envService := env.NewEnvService(ctx)
	componentName := envService.GetString(env.EnvComponentName)
	partName := envService.GetString(env.EnvPartName)
	stage := envService.GetString(env.EnvStage)
	baseService := fmt.Sprintf("%s-%s-%s", stage, componentName, partName)

	keepWarmList := []warmup.WarmupLambda{
		{Name: baseService + "-api-api-handler", Count: 1},
		{Name: baseService + "-api-cron", Count: 1},
	}

	warmup.New(ctx).Warmup(ctx, keepWarmList)
}
