package lambdas

import (
	"context"
	"os"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	AWSLambda "github.com/aws/aws-sdk-go-v2/service/lambda"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/lambda"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
)

func init() {
	os.Setenv("TEVALIS_API_URL", "https://dummy-tevalis-api-url")
	os.Setenv("ZELLER_DEVID", "dummy-zeller-dev-id")
}

func TestCanInstantiateLambdaClass(t *testing.T) {
	New(context.Background())
}

func TestCanInvokeApiHandler(t *testing.T) {
	ctx := context.Background()
	lambda := New(ctx)
	result, err := lambda.ApiHandler(ctx, events.APIGatewayProxyRequest{})

	assert.Nil(t, err)

	if result.StatusCode != 404 {
		t.Errorf("Expected status code 400, got %d", result.StatusCode)
	}
}

type MockLambda struct {
	mock.Mock
}

func (m *MockLambda) Invoke(ctx context.Context, params *AWSLambda.InvokeInput, optFns ...func(*AWSLambda.Options)) (*AWSLambda.InvokeOutput, error) {
	args := m.Called(ctx, *params.FunctionName, params.Payload, optFns)
	return args.Get(0).(*AWSLambda.InvokeOutput), args.Error(1)
}

func TestCanInvokeWarmupHandler(t *testing.T) {
	m := new(MockLambda)
	m.On("Invoke", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&AWSLambda.InvokeOutput{}, nil).Times(2)
	ctx := context.WithValue(context.Background(), lambda.LambdaService{}, m)
	os.Setenv(string(env.EnvComponentName), "cpi")
	os.Setenv(string(env.EnvPartName), "tevalis-engine")
	os.Setenv(string(env.EnvStage), "test")
	lambda := New(ctx)
	lambda.WarmupHandler(ctx)

	m.AssertExpectations(t)
}
