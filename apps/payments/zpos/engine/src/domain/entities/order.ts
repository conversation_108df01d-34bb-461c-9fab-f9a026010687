import { OrderStatus } from '@npco/component-dto-order/dist';

import { AfterLoad, Column, Entity, OneToMany, PrimaryColumn } from 'typeorm';

import { BaseModel } from '../models/baseModel';
import { CatalogSettingsSnapshot } from '../types';

import type { OrderDiscount } from './orderDiscount';
import type { OrderItem } from './orderItem';
import type { OrderPayment } from './orderPayment';
import type { OrderServiceCharge } from './orderServiceCharge';
import { ColumnBigIntToNumberTransformer } from './transformers';

@Entity('Orders')
export class Order {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'enum', enum: OrderStatus })
  status!: OrderStatus;

  @Column({ type: 'text' })
  referenceNumber!: string;

  @Column({ type: 'jsonb' })
  catalogSettings!: CatalogSettingsSnapshot;

  @Column({ type: 'uuid' })
  siteUuid!: string;

  @Column({ type: 'uuid' })
  createdFromDeviceUuid?: string;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  paidAmount?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  dueAmount?: number;

  @Column({ type: 'text' })
  currency!: string;

  @Column({ type: 'integer' })
  currencyMinorUnit!: number;
  /**
   * totalAmount - deprecated, use orderAmount instead
   */

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalAmount!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  orderAmount!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  subtotalAmount!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalSurcharge?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalSurchargedGst?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalGst?: number;

  @Column({
    type: 'bigint',
  })
  orderGst?: number;

  @Column({ type: 'int' })
  cashRoundingAdjustment?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalTips?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalDiscount?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalServiceCharge?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  orderDisplayAmount?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalChange?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  totalAmountTendered?: number;

  @Column({ type: 'integer' })
  createdTime!: number;

  @Column({ type: 'integer' })
  updatedTime?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  updatedTimeInMilliseconds?: number;

  @Column({ type: 'integer' })
  paidTime?: number;

  @Column({ type: 'varchar' })
  createdTimestampLocal?: string;

  @OneToMany('OrderPayment', 'order', { cascade: true })
  payments?: OrderPayment[];

  @OneToMany('OrderItem', 'order', {
    cascade: true,
  })
  items?: OrderItem[];

  @OneToMany('OrderDiscount', 'order', {
    cascade: true,
  })
  discounts?: OrderDiscount[];

  @OneToMany('OrderServiceCharge', 'order', {
    cascade: true,
  })
  serviceCharges?: OrderServiceCharge[];

  @AfterLoad()
  afterLoad? = () => {
    // This method is called after the order is loaded from the database.
    // If the currencyMinorUnit is 4 (centi-cents), convert it to 2 (cents_ and update the amounts accordingly.
    // This is to support API requests during the migration.
    // After the migration is complete, this can be removed.
    const baseModel = new BaseModel();
    baseModel.convertOrderAmountToCentsIfRequired(this);
  };
}
