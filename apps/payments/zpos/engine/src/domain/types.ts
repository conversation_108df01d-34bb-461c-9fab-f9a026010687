import type {
  CatalogDiscountConfig,
  CatalogItem,
  CatalogModifier,
  CatalogServiceChargeConfig,
  CatalogUnit,
} from '@npco/component-dto-catalog/dist';
import type { ISO3166, Money } from '@npco/component-dto-core/dist/types';
import type { OrderItemType, OrderStatus, TenderType } from '@npco/component-dto-order/dist';
import type { Transaction, TransactionStatus } from '@npco/component-dto-transaction/dist/types';

export type CurrencyRateConfig = {
  [key: string]: {
    rate: number;
    fractionDigits: number;
  };
};

export enum GetOrdersFilterInputStatus {
  OPEN = 'OPEN',
  PAID = 'PAID',
}

export type GetOrdersFilterInput = {
  siteUuid?: string;
  status?: OrderStatus;
};

export type GetOrdersInput = {
  limit: number;
  filter: GetOrdersFilterInput;
};

export type CreateOrderDiscountInput = {
  id: string;
  catalogDiscountUuid?: string;
  catalogDiscount?: any;
  name: string;
  config: CatalogDiscountConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderServiceChargeInput = {
  id: string;
  catalogServiceChargeUuid?: string;
  catalogServiceCharge?: any;
  name: string;
  config: CatalogServiceChargeConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderItemModifierInput = {
  id: string;
  catalogModifierUuid?: string;
  catalogModifier?: any;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
};

export type CreateOrderItemInput = {
  id: string;
  catalogItemUuid?: string;
  type: OrderItemType;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  discounts?: CreateOrderDiscountInput[];
  serviceCharges?: CreateOrderServiceChargeInput[];
  taxes?: CatalogTaxInput[];
  modifiers?: CreateOrderItemModifierInput[];
  variantName?: string;
};

export type CreateOrderInput = {
  id: string;
  entityUuid: string;
  siteUuid: string;
  createdFromDeviceUuid?: string;
  updatedTime: string;
  catalogSettings: CatalogSettingsSnapshot;
  createdTimestampLocal?: string;
  items: CreateOrderItemInput[];
  discounts?: CreateOrderDiscountInput[];
  serviceCharges?: CreateOrderServiceChargeInput[];
  currency?: string;
};

export type OrderDiscount = {
  catalogDiscountUuid?: string;
  name: string;
  config: CatalogDiscountConfig;
  value: string;
  discountedAmount: Money;
  ordinal: number;
};

export type OrderServiceCharge = {
  catalogServiceChargeUuid?: string;
  name: string;
  config: CatalogServiceChargeConfig;
  value: string;
  serviceChargeAmount: Money;
  ordinal: number;
};

export type OrderItemModifier = {
  catalogModifier: CatalogModifier;
  name: string;
  price: Money;
  ordinal: number;
  unit: CatalogUnit;
  quantity: number;
};

export type OrderItemAttribute = {
  attributeName: string;
  attributeValue: string;
};

export type OrderItem = {
  id: string;
  catalogItem: CatalogItem;
  name: string;
  price: Money;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  discounts: OrderDiscount[];
  serviceCharges: OrderServiceCharge[];
  taxes: CatalogTax[];
  modifiers: OrderItemModifier[];
  variantName?: string;
};

export type Order = {
  id: string;
  entityUuid: string;
  status: OrderStatus;
  referenceNumber: string;
  siteName: string;
  siteUuid: string;
  items: OrderItem[];
  discounts: OrderDiscount[];
  serviceCharges: OrderServiceCharge[];
  transactions: Transaction[];
  paidAmount?: Money;
  dueAmount?: Money;
  subtotalAmount?: Money;
  totalAmount?: Money;
  totalSurcharge?: Money;
  totalGst?: Money;
  totalDiscount?: Money;
  totalServiceCharge?: Money;
  totalTips?: Money;
  itemsApplyTax: boolean;
  itemsTaxInclusive: boolean;
  createdTime: number;
  paidTime?: number;
  createdTimestampLocal?: string;
};

export type CatalogTaxInput = {
  enabled: boolean;
  name: string;
  percent?: number;
};

export type CatalogTax = {
  enabled: boolean;
  name: string;
  percent?: number;
};

export type OrderTableConnection = {
  orders: Order[];
  nextToken?: string;
};

export type CatalogSettingsSnapshot = {
  itemsTaxInclusive: boolean;
  itemsApplyTax: boolean;
  autoSkuEnabled: boolean;
};

export type MultiTenderTransaction = {
  id: string;
  amount: number;
  saleAmount: number;
  surchargeAmount?: number;
  tipAmount?: number;
  amountTendered?: number;
  change?: number;
  taxAmounts: { name: string; amount: number }[];
  status: TransactionStatus;
  timestamp: string;
  timestampLocal?: string;
  tenderType: TenderType;
  cashRoundingAdjustment?: number;
  tenderSubType?: string;
  currency?: ISO3166;
  note?: string;
};
