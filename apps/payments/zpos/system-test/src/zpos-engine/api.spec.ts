import { DynamodbClient, sleep } from '@npco/bff-systemtest-utils';
import { OrderStatus } from '@npco/component-dto-order/dist/types';

import axios from 'axios';
import { Client } from 'pg';
import { v4 } from 'uuid';

import { getApiEndpoint, getDbClientOptions, region, sendMutationRequest } from './utils';

describe('ZPOS API test suite', () => {
  let baseEndpoint: string;
  let endpointV1: string;
  let client: Client;

  beforeAll(async () => {
    const options = await getDbClientOptions();
    console.log('options:', options);
    client = new Client(options);
    await client.connect();
    try {
      await client.query(`SET search_path TO ${options.search_path}`);
    } catch (err) {
      console.error(err);
    }
    baseEndpoint = await getApiEndpoint();
    endpointV1 = `${baseEndpoint}/v1/`;
  });

  afterAll(async () => {
    await client.end();
  });

  const getCreateOrderRequestInput = () => {
    return {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      createdFromDeviceUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true },
      updatedTime: new Date().toISOString(),
      items: [
        {
          id: v4(),
          name: 'test item',
          price: '10000',
          ordinal: 1,
          type: 'SINGLE',
          unit: 'HOUR',
          quantity: 1,
          discounts: [
            {
              id: v4(),
              catalogDiscountUuid: v4(),
              name: v4(),
              config: 'PERCENTAGE',
              value: '10',
            },
          ],
          serviceCharges: [
            {
              id: v4(),
              catalogServiceChargeUuid: v4(),
              name: v4(),
              config: 'PERCENTAGE',
              value: '10',
            },
          ],
          modifiers: [
            {
              id: v4(),
              name: v4(),
              price: '1000',
              ordinal: 1,
              unit: 'QUANTITY',
              quantity: 1,
            },
          ],
          taxes: [{ name: 'GST', enabled: true, percent: 10 }],
        } as any,
      ],
      discounts: [
        {
          id: v4(),
          catalogDiscountUuid: v4(),
          name: v4(),
          config: 'AMOUNT',
          value: '100',
        },
      ],
      serviceCharges: [
        {
          id: v4(),
          catalogServiceChargeUuid: v4(),
          name: v4(),
          config: 'AMOUNT',
          value: '100',
        },
      ],
    };
  };
  describe('OrderApi', () => {
    it('createOrder: should create order in database', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(apiResult.status).toBe(200);
      const { data: order } = apiResult;
      expect(order).toBeDefined();
      expect(order.id).toBe(orderInput.id);
      expect(order.entityUuid).toBe(orderInput.entityUuid);
      expect(order.createdFromDeviceUuid).toBe(orderInput.createdFromDeviceUuid);
      expect(order.siteUuid).toBe(orderInput.siteUuid);
      expect(order.items).toHaveLength(1);
      expect(order.items[0].name).toBe(orderInput.items[0].name);
      expect(order.items[0].price).toEqual({ currency: 'AUD', value: '10000' });
      expect(order.items[0].ordinal).toBe(orderInput.items[0].ordinal);
      expect(order.items[0].type).toBe(orderInput.items[0].type);
      expect(order.items[0].unit).toBe(orderInput.items[0].unit);
      expect(order.items[0].quantity).toBe(orderInput.items[0].quantity);
      expect(order.items[0].subtotalAmount).toEqual({ currency: 'AUD', value: '12000' });
      expect(order.items[0].discounts).toHaveLength(1);
      expect(order.items[0].discounts[0].catalogDiscountUuid).toBe(
        orderInput.items[0].discounts[0].catalogDiscountUuid,
      );
      expect(order.items[0].discounts[0].name).toBe(orderInput.items[0].discounts[0].name);
      expect(order.items[0].discounts[0].config).toBe(orderInput.items[0].discounts[0].config);
      expect(order.items[0].discounts[0].value).toBe(orderInput.items[0].discounts[0].value);
      expect(order.items[0].modifiers).toHaveLength(1);
      expect(order.items[0].modifiers[0].name).toBe(orderInput.items[0].modifiers[0].name);
      expect(order.items[0].modifiers[0].price).toEqual({ currency: 'AUD', value: '1000' });
      expect(order.items[0].modifiers[0].subtotalAmount).toEqual({ currency: 'AUD', value: '1000' });
      expect(order.items[0].modifiers[0].ordinal).toBe(orderInput.items[0].modifiers[0].ordinal);
      expect(order.items[0].modifiers[0].unit).toBe(orderInput.items[0].modifiers[0].unit);
      expect(order.items[0].modifiers[0].quantity).toBe(orderInput.items[0].modifiers[0].quantity);
      expect(order.items[0].taxes).toHaveLength(1);
      expect(order.items[0].taxes[0].name).toBe(orderInput.items[0].taxes[0].name);
      expect(order.items[0].taxes[0].enabled).toBe(orderInput.items[0].taxes[0].enabled);
      expect(order.items[0].taxes[0].percent).toBe(orderInput.items[0].taxes[0].percent);
      expect(order.discounts).toHaveLength(1);
      expect(order.discounts[0].catalogDiscountUuid).toBe(orderInput.discounts[0].catalogDiscountUuid);
      expect(order.discounts[0].name).toBe(orderInput.discounts[0].name);
      expect(order.discounts[0].config).toBe(orderInput.discounts[0].config);
      expect(order.discounts[0].value).toBe(orderInput.discounts[0].value);
      expect(order.totalDiscount).toEqual({ currency: 'AUD', value: '1300' });

      expect(order.serviceCharges).toHaveLength(1);
      expect(order.serviceCharges[0].catalogServiceChargeUuid).toBe(
        orderInput.serviceCharges[0].catalogServiceChargeUuid,
      );
      expect(order.serviceCharges[0].name).toBe(orderInput.serviceCharges[0].name);
      expect(order.serviceCharges[0].config).toBe(orderInput.serviceCharges[0].config);
      expect(order.serviceCharges[0].value).toBe(orderInput.serviceCharges[0].value);
      expect(order.totalServiceCharge).toEqual({ currency: 'AUD', value: '1200' });
      expect(order.items[0].serviceCharges).toHaveLength(1);
      expect(order.items[0].serviceCharges[0].catalogServiceChargeUuid).toBe(
        orderInput.items[0].serviceCharges[0].catalogServiceChargeUuid,
      );
      expect(order.items[0].serviceCharges[0].name).toBe(orderInput.items[0].serviceCharges[0].name);
      expect(order.items[0].serviceCharges[0].config).toBe(orderInput.items[0].serviceCharges[0].config);
      expect(order.items[0].serviceCharges[0].value).toBe(orderInput.items[0].serviceCharges[0].value);
    });

    it('updateOrder: should update existing order in database', async () => {
      const input = getCreateOrderRequestInput();
      // const apiResult = await sendMutationRequest({ input, requestName: 'createOrder' });

      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input }), {
        headers: { 'Content-Type': 'application/json' },
      });

      expect(apiResult.status).toBe(200);
      const { data: orderUpdateInput } = apiResult;
      orderUpdateInput.items[0].name = 'updated item';
      delete orderUpdateInput.items[0].modifiers[0].subtotalAmount;
      // act
      const updateResponse = await sendMutationRequest({
        input: {
          ...input,
          updatedTime: new Date(new Date().getTime() + 1000).toISOString(),
          items: [
            {
              ...orderUpdateInput.items[0],
              price: '100000',
              modifiers: [
                {
                  ...orderUpdateInput.items[0].modifiers[0],
                  price: '11200',
                },
              ],
            },
          ],
          discounts: orderUpdateInput.discounts,
          serviceCharges: orderUpdateInput.serviceCharges,
        },
        requestName: 'createOrder',
      });

      // verify
      expect(updateResponse?.status).toBe(200);
      expect(updateResponse?.data.items[0]!.name).toBe(orderUpdateInput.items[0]!.name);
      expect(updateResponse?.data.items[0]!.subtotalAmount).toEqual({ currency: 'AUD', value: '121200' });
    });

    it('cancelOrder: should cancel order in database', async () => {
      const input = getCreateOrderRequestInput();
      const apiResult = await sendMutationRequest({ input, requestName: 'createOrder' });
      expect(apiResult.status).toBe(200);
      const { data: order } = apiResult;
      // act
      const cancelResponse = await sendMutationRequest({
        input: { id: order.id, entityUuid: order.entityUuid },
        requestName: 'cancelOrder',
      });
      // verify
      expect(cancelResponse?.status).toBe(200);
      expect(cancelResponse?.data.status).toBe('CANCELLED');
    });

    it('createOrderPaymentAndFinalise: should checkout an order with cash payment', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      //  const apiResult = await sendMutationRequest({ input: orderInput, requestName: 'createOrder' });
      expect(apiResult.status).toBe(200);
      const { data: order } = apiResult;
      console.log('created order uuid: ', order.id);
      await axios.post(
        `${endpointV1}createOrderPayment`,
        JSON.stringify({
          input: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: 'CASH',
            amount: 50,
            surchargeAmount: 0,
            timestampLocal: 'xx',
            tipAmount: 0,
            taxAmounts: [{ name: 'gst', amount: 1 }],
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const result = await client.query({
        text: `SELECT * FROM "OrderPayments" WHERE "orderId" = $1`,
        values: [order.id],
      });
      expect(result.rows.length).toEqual(1);
      const cashPayment = result.rows[0];
      expect(cashPayment.taxAmounts).toEqual([{ name: 'gst', amount: 1 }]);
      expect(cashPayment.amount).toEqual('50');
      await axios.post(
        `${endpointV1}createOrderPaymentAndFinalise`,
        JSON.stringify({
          input: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: 'OTHER',
            tenderSubType: 'loyalty points',
            amount: 30,
            tipAmount: 1,
            taxAmounts: [{ name: 'gst', amount: 2 }],
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const result2 = await client.query({
        text: `SELECT * FROM "OrderPayments" WHERE "orderId" = $1`,
        values: [order.id],
      });
      expect(result2.rows.length).toEqual(2);
      const otherPayment = result2.rows.find((p) => p.tenderType === 'OTHER');
      expect(otherPayment.taxAmounts).toEqual([{ name: 'gst', amount: 2 }]);
      expect(otherPayment.tips).toEqual('1');
      expect(otherPayment.amount).toEqual('30');
    });

    it('updateOrderStatus: updating status as paid, updating the time', async () => {
      const orderInput = {
        id: v4(),
        entityUuid: v4(),
        siteUuid: v4(),
        createdFromDeviceUuid: v4(),
        catalogSettings: { itemsTaxInclusive: true },
        updatedTime: new Date().toISOString(),
        items: [
          {
            id: v4(),
            name: 'test item',
            price: '1000',
            ordinal: 1,
            type: 'SINGLE',
            unit: 'HOUR',
            quantity: 1,
            taxes: [],
            discounts: [
              {
                id: v4(),
                catalogDiscountUuid: v4(),
                name: v4(),
                config: 'AMOUNT',
                value: '1000',
              },
            ],
          } as any,
        ],
      };
      const createOrder = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(createOrder.status).toBe(200);
      await sleep(2000);
      const markZeroDollarSaleAsPaidInput = {
        orderUuid: orderInput.id,
        entityUuid: orderInput.entityUuid,
        status: OrderStatus.PAID,
      };
      const apiResult = await axios.post(
        `${endpointV1}updateOrderStatus`,
        JSON.stringify({ input: markZeroDollarSaleAsPaidInput }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      expect(apiResult).toBeDefined();
      expect(apiResult.status).toBe(200);
      expect(apiResult.data.status).toBe('PAID');
      expect(apiResult.data.updatedTime).toBeDefined();
      expect(apiResult.data.updatedTime).toBeGreaterThan(createOrder.data.updatedTime);
      expect(apiResult.data.paidTime).toBeDefined();
    });

    it('updateOrderPayment: should update cash payment tips', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      //  const apiResult = await sendMutationRequest({ input: orderInput, requestName: 'createOrder' });
      expect(apiResult.status).toBe(200);
      const { data: order } = apiResult;
      console.log('created order uuid: ', order.id);
      await axios.post(
        `${endpointV1}createOrderPayment`,
        JSON.stringify({
          input: {
            id: v4(),
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            tenderType: 'CASH',
            amount: 50,
            surchargeAmount: 0,
            timestampLocal: 'xx',
            tipAmount: 0,
            taxAmounts: [{ name: 'gst', amount: 1 }],
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const result = await client.query({
        text: `SELECT * FROM "OrderPayments" WHERE "orderId" = $1`,
        values: [order.id],
      });
      expect(result.rows.length).toEqual(1);
      const cashPayment = result.rows[0];
      expect(cashPayment.taxAmounts).toEqual([{ name: 'gst', amount: 1 }]);
      expect(cashPayment.amount).toEqual('50');
      await axios.post(
        `${endpointV1}updateOrderPayment`,
        JSON.stringify({
          input: {
            orderUuid: order.id,
            entityUuid: order.entityUuid,
            paymentUuid: cashPayment.id,
            updatedTime: new Date().toISOString(),
            tips: 10,
          },
        }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const result2 = await client.query({
        text: `SELECT * FROM "OrderPayments" WHERE "orderId" = $1`,
        values: [order.id],
      });
      expect(result2.rows.length).toEqual(1);
      const updatedCashPayment = result2.rows[0];
      expect(updatedCashPayment.tips).toEqual('10');
    });
  });

  describe('migration', () => {
    it('markOrderAsPaid', async () => {
      const dbClient = new DynamodbClient({ region });
      const txnTableName = `${process.env.STAGE}-zpos-engine-dynamodb-Transactions`;
      const transactionUuid = v4();
      await dbClient.put({
        TableName: txnTableName,
        Item: {
          id: transactionUuid,
          type: 'transaction.',
          source: 'ZELLER_POS',
          externalReference: v4(),
          status: 'APPROVED',
        },
      });
      // act
      const apiResult = await axios.post(
        `${endpointV1}markOpenOrderAsPaid`,
        JSON.stringify({ input: { transactionUuid } }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      // verify
      expect(apiResult.status).toBe(200);
    });

    it('getOrderPaidEventMissingAttributes', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      const { data: order } = apiResult;
      const apiResult2 = await axios.post(
        `${endpointV1}getOrderPaidEventMissingAttributes`,
        JSON.stringify({ input: { id: order.id, entityUuid: order.entityUuid } }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const { data: order2 } = apiResult2;
      expect(order2.items.length).toEqual(1);
      expect(order2.discounts.length).toEqual(1);
      expect(order2.siteUuid).toEqual(orderInput.siteUuid);
      expect(order2.createdFromDeviceUuid).toEqual(orderInput.createdFromDeviceUuid);
    });

    // set to true when exclude modifier price
    xit('should fix order subtotalAmount', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(apiResult.status).toBe(200);
      const { data: order } = apiResult;
      expect(order).toBeDefined();
      expect(order.id).toBe(orderInput.id);
      expect(order.items).toHaveLength(1);
      expect(order.items[0].subtotalAmount).toEqual({ currency: 'AUD', value: '11000' });
      expect(order.items[0].modifiers[0].id).toBeDefined();
      await client.query({
        text: `UPDATE "OrderItems" SET "subtotalAmount" = $1 WHERE id = $2`,
        values: [13000, order.items[0].id],
      });
      await client.query({
        text: `UPDATE "OrderItems" SET "subtotalAmount" = $1 WHERE id = $2`,
        values: [13000, order.items[0].modifiers[0].id],
      });
      await axios.post(
        `${endpointV1}recalculateOrderItem`,
        JSON.stringify({ input: { id: order.id, entityUuid: order.entityUuid } }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      const orderItemsResult = await client.query({
        text: `SELECT * FROM "OrderItems" WHERE id = $1`,
        values: [order.items[0].id],
      });
      expect(orderItemsResult.rows[0].subtotalAmount).toEqual('12000');

      const orderModifierItemsResult = await client.query({
        text: `SELECT * FROM "OrderItems" WHERE id = $1`,
        values: [order.items[0].modifiers[0].id],
      });
      expect(orderModifierItemsResult.rows[0].subtotalAmount).toEqual('1000');
    });

    it('rematerialiseOrder', async () => {
      const orderInput = getCreateOrderRequestInput();
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: orderInput }), {
        headers: { 'Content-Type': 'application/json' },
      });
      const { data: order } = apiResult;
      const apiResult2 = await axios.post(
        `${endpointV1}rematerialiseOrder`,
        JSON.stringify({ input: { id: order.id, entityUuid: order.entityUuid } }),
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      expect(apiResult2.status).toBe(200);
    });
  });
});
