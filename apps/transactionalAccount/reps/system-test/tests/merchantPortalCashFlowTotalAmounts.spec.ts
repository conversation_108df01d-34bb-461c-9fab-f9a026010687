import { sleep } from '@npco/bff-systemtest-utils';
import type { ContactCreatedEventDto } from '@npco/component-dto-addressbook';
import type {
  DebitCardTransactionV2,
  SavingsAccountTransactionCreatedEventDto,
} from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { assertTotalAmounts } from '../utils/assertions/totalAmounts';
import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { MerchantPortalEnvService } from '../utils/envService';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';
import { getSavingsAccountTxnDto } from '../utils/savingsAccountTransaction/savingsAccountTransaction';
import { createSavingsAccountTransaction } from '../utils/savingsAccountTransaction/savingsAccountTransactionSqs';
import { ApiTestHelper } from '../utils/testHelper';

const currentDate: any = new Date().toISOString().split('T')[0];

describe('Merchant Portal BFF API - Cash flow total amounts test suite', () => {
  const domicile = getDomicileByRegion();
  const mpEnvService = new MerchantPortalEnvService();
  const apiTestHelper = new ApiTestHelper(mpEnvService);

  let client: Client;

  const invoke = async (lambda: string, args: any) =>
    apiTestHelper.invokeLambda(`${mpEnvService.COMPONENT_NAME}-reps-${lambda}`, {
      args,
    });

  const getCashFlowTotalAmountsHandler = async (args: any) => invoke('getCashFlowTotalAmountsHandler', args);

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await sleep();
    const options = await getDbClientOptions();

    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  describe('get cash flow total amounts api', () => {
    const entityUuid = apiTestHelper.getEntityUuid();
    const savingsAccountTxnAcountUuid = uuid();
    const dcaTxnAccountUuid = uuid();
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
    });
    let paymentInstrumentDetails: PaymentInstrumentCreatedDto;
    let contactDetails: ContactCreatedEventDto;
    let dcaTxnDto: DebitCardTransactionV2;
    let savingsAccountTxnDto: SavingsAccountTransactionCreatedEventDto;

    beforeAll(async () => {
      contactDetails = (await createContact(dtoContact, client, domicile)).rows[0];

      paymentInstrumentDetails = (
        await createPaymentInstrument(
          {
            ...paymentInstrumentDto,
            contactUuid: contactDetails.contactUuid,
          },
          client,
          domicile,
        )
      ).rows[0];

      dcaTxnDto = getDcaTxnDto({
        id: dcaTxnAccountUuid,
        entityUuid,
        type: DebitCardTransactionTypeV2.DE_OUT,
        payerDetails: {
          recipientUuid: uuid(),
          senderUuid: uuid(),
        },
        payeeDetails: {
          recipientUuid: paymentInstrumentDetails.paymentInstrumentUuid,
        },
      });

      savingsAccountTxnDto = getSavingsAccountTxnDto({
        id: savingsAccountTxnAcountUuid,
        entityUuid,
        type: DebitCardTransactionTypeV2.INTEREST,
      });

      await Promise.all([
        createDebitCardAccountTransaction(dcaTxnDto, client, domicile),
        createSavingsAccountTransaction(savingsAccountTxnDto, client, domicile),
      ]);
    });

    describe('single account', () => {
      it.each([dcaTxnAccountUuid, savingsAccountTxnAcountUuid])(
        'should be able to get all records for single account',
        async (accountUuid) => {
          const input = {
            entityUuid,
            date: currentDate,
            accountUuid,
          };

          const response = await getCashFlowTotalAmountsHandler(input);

          expect(response).not.toBeUndefined();
          expect(response.range).toEqual(
            expect.objectContaining({
              start: expect.any(String),
              end: expect.any(String),
            }),
          );
          expect(response.timeZone).toEqual(expect.any(String));
          expect(response.accountUuid).toEqual(accountUuid);
          assertTotalAmounts(response);
        },
      );
    });
    describe('all accounts', () => {
      it('should be able to get all records ', async () => {
        const input = {
          entityUuid,
          date: currentDate,
        };

        const response = await getCashFlowTotalAmountsHandler(input);

        expect(response).toBeDefined();
        expect(response.range).toEqual(
          expect.objectContaining({
            start: expect.any(String),
            end: expect.any(String),
          }),
        );
        expect(response.timeZone).toEqual(expect.any(String));

        assertTotalAmounts(response);
      });
    });
  });
});
