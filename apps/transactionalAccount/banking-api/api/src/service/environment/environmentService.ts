import { EnvironmentService as BaseEnvironmentService } from '@npco/component-bff-core/dist/config/envService';

export class EnvironmentService extends BaseEnvironmentService {
  public componentName: string;

  public cbsApiEndpoint: string;

  public amsApiEndpoint: string;

  public amsApiEndpointVersion: string;

  public projectionSqsUrl: string;

  public accountStatementExportsSqsUrl: string;

  public interestSummaryExportsSqsUrl: string;

  public dcaRematerialisationSqsUrl: string;

  public dcaTransactionLookupSqsUrl: string;

  public acccountStatementExportBucket: string;

  public interestSummaryExportBucket: string;

  public bankingProductTableName: string;

  public merchantTableName: string;

  public debitCardIdGsi: string;

  public sortKeyGsi: string;

  public sourceEmailAddress: string;

  public cardLogoUploadBucket: string;

  public materialiseIncomingExternalNonApprovedTransaction = false;

  public rawReceiptDocUploadsBucket: string;

  public receiptDocProcessedBucket: string;

  public maxReceiptDocumentUploadsUrls: number;

  public maxReceiptDocumentFileNameLength: number;

  constructor() {
    super();
    this.componentName = this.configService.get('COMPONENT_NAME', '');
    this.cbsApiEndpoint = this.configService.get('CBS_API_ENDPOINT', '');
    this.amsApiEndpoint = this.configService.get('AMS_API_ENDPOINT', '');
    this.amsApiEndpointVersion = this.configService.get('AMS_API_ENDPOINT_VERSION', 'v1');
    this.projectionSqsUrl = this.configService.get('PROJECTION_SQS_URL', '');
    this.bankingProductTableName = this.configService.get('BANKING_PRODUCT_TABLE', 'BankingProduct');
    this.merchantTableName = this.configService.get('MERCHANT_TABLE', 'Merchant');
    this.debitCardIdGsi = this.configService.get('DEBIT_CARD_ID_GSI', 'debitCardIdGsi');
    this.sortKeyGsi = this.configService.get('SORT_KEY_GSI', 'sortKeyGsi');
    this.accountStatementExportsSqsUrl = this.configService.get(
      'ACCOUNT_STATEMENT_EXPORTS_SQS_URL',
      'https://fake.accountStatementExportsSqsUrl.com',
    );
    this.interestSummaryExportsSqsUrl = this.configService.get(
      'INTEREST_SUMMARY_EXPORTS_SQS_URL',
      'https://fake.interestSummaryExportsSqsUrl.com',
    );
    this.dcaRematerialisationSqsUrl = this.configService.get(
      'DCA_REMATERIALISATION_SQS_URL',
      'https://fake.dcaRematerialisationSqsUrl.com',
    );
    this.dcaTransactionLookupSqsUrl = this.configService.get(
      'DCA_TRANSACTION_LOOKUP_SQS_URL',
      'https://fake.dcaTransactionLookupSqsUrl.com',
    );
    this.acccountStatementExportBucket = this.configService.get(
      'ACCOUNT_STATEMENT_EXPORT_BUCKET',
      'account-statement-export-bucket',
    );
    this.interestSummaryExportBucket = this.configService.get(
      'INTEREST_SUMMARY_EXPORT_BUCKET',
      'interest-summary-export-bucket',
    );
    this.sourceEmailAddress = this.configService.get('SOURCE_EMAIL_ADDRESS', 'Zeller <<EMAIL>>');
    this.dynamoDbMarshallOptions = {
      convertEmptyValues: false,
      removeUndefinedValues: true,
      convertClassInstanceToMap: true,
    };
    this.cardLogoUploadBucket = this.configService.get('CARD_LOGO_UPLOAD_BUCKET', 'card-logo-upload-bucket');
    this.materialiseIncomingExternalNonApprovedTransaction =
      this.configService.get('MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION', false) === 'true';
    this.rawReceiptDocUploadsBucket = this.configService.get('S3_RECEIPT_DOCUMENT_UPLOADS', '');
    this.receiptDocProcessedBucket = this.configService.get('S3_RECEIPT_DOCUMENT_PROCESSED', '');
    this.maxReceiptDocumentUploadsUrls = 30;
    this.maxReceiptDocumentFileNameLength = 256;
  }
}
