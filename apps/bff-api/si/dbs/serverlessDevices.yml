service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-devices

plugins:
  - serverless-esbuild
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-plugin-lambda-dead-letter
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  stackName: ${self:service}
  serviceName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}

  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  dynamodbStackName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api-dynamodb
  appsyncStackName: ${self:provider.serviceName}-appsync
  deviceTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  modelSerialGsi: ${env:CACHE_MODELSERIAL_GSI}
  cacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  accessTokenGsi: ${env:ACCESS_TOKEN_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  siteGsi: ${env:SITE_GSI}
  typeGsi: ${env:TYPE_GSI}
  entityGsi: ${env:ENTITY_GSI}

  appSyncEndpoint: ${self:custom.appSyncAutoGeneratedEndpoint}
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, ''}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  auth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api/AUTH0_CLIENT_SECRET}

  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  exclude:
    - node_modules/**

custom:
  esbuild:
    bundle: true
    keepNames: true
    plugins: esbuild_plugin.js
    exclude:

      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
  prune:
    automatic: true
    includeLayers: true
    number: 5
  tmpDeviceKeysTableName: ${self:provider.stackName}-tmp-DeviceKeysDb
  rkiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-rki-endpoint}/v1/rki/generate/tr31-key-block'
  usePgsRki: ${env:USE_PGS_RKI}
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  streamArn: ${param:dbStreamArn, "${cf:${self:provider.serviceName}-dynamodb.DeviceTableStreamArn, ''}"}
  deviceStreamHandlerFilterPatterns:
    - prefix: deposit.
    - prefix: transaction.
    - prefix: device.settings
    - prefix: device.software
    - prefix: sim.core
    - prefix: contact.core
  
  appSyncEndpoints:
    dev: https://devices.myzeller.${opt:stage}/graphql
    staging: https://devices.myzeller.show/graphql
    prod: https://devices.myzeller.com/graphql
    st: ${self:custom.appSyncAutoGeneratedEndpoint}

  appSyncAutoGeneratedEndpoint:
      Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'
  permissionsTableReadRolePolicyArn:
    Fn::ImportValue: '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-api-permissionsTableReadRolePolicyArn'
  domicileLookupTableReadRolePolicyArn:
    Fn::ImportValue: '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'

functions:
  - ${file(resources/devices/lambdas.yml)}

resources:
  - ${file(resources/devices/resolvers.yml)}
  - ${file(resources/devices/iam.yml)}
