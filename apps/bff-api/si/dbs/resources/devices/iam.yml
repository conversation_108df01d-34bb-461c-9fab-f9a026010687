Resources:
  getDeviceUuidRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-GetDeviceUUIDDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.cacheTableName}/index/${self:provider.modelSerialGsi}'
        - PolicyName: ${self:provider.stackName}-GetDevcieUUIDLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getDeviceUuid:*'

  getDeviceSettingsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-GetDeviceSettingsDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
        - PolicyName: ${self:provider.stackName}-GetDeviceSettingsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getDeviceSettings:*'

  getCustomerSettingsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-getCustomerSettingsRoleDBPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}/index/${self:provider.siteGsi}'
              - Effect: Allow
                Action:
                  - dynamodb:BatchGetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.deviceTableName}'
        - PolicyName: ${self:provider.stackName}-getCustomerSettingsRoleLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getCustomerSettings:*'

  updateDeviceSettingsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-UpdateDeviceSettingsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateDeviceSettings:*'

  createUniqueDeviceNameRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-CreateUniqueDeviceNameLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-createUniqueDeviceName:*'

  onDeviceSettingsUpdateRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${opt:stage}-onDeviceSettingsUpdateLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-onDeviceSettingsUpdateHandler:*'

  updateDeviceInformationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-updateDeviceInformationHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateDeviceInformationHandler:*'

  requestKeyInitialisationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-requestKeyInitialisationHandlerLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:custom.tmpDeviceKeysTableName}'
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-requestKeyInitialisationHandler:*'

  checkSoftwareUpdateRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableWriteItemRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${opt:stage}-checkSoftwareUpdateLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-checkSoftwareUpdateHandler:*'

  getDeviceInformationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-deviceTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - ${self:custom.permissionsTableReadRolePolicyArn}
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-GetDeviceInformationLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getDeviceInformation:*'
