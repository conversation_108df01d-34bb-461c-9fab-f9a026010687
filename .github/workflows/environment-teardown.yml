name: environment-teardown

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/**
      - apps/accounting/**
      - apps/bff-api/**
      - apps/core/**
      - apps/onboarding/**
      - apps/transactionalAccount/**
      - .github/workflows/*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      environment:
        type: string
        required: true

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: bff
      part_name: teardown

  teardown-environment:
    runs-on: ubuntu-latest
    environment: st-bff-teardown
    needs:
      - detect-env
      - assume-roles
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ toJson(from<PERSON><PERSON>(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}
          aws_region: ${{ vars.SYDNEY_REGION }}

      - id: find-stage
        shell: bash
        run: |
          if [[ "${{github.event_name }}" == 'workflow_dispatch' ]]; then
            stage=${{ needs.detect-env.outputs.environment }}
          else
            stage=st${{github.event.pull_request.number}}
          fi
          echo "stage=$stage" >> $GITHUB_OUTPUT

      - name: check stage
        shell: bash
        if: contains(fromJson('["dev", "staging", "prod"]'), steps.find-stage.outputs.stage)
        run: |
          echo "dont tear down ${{ steps.find-stage.outputs.stage }} resources"
          exit 1

      - name: teardown
        shell: bash
        run: |
          cd apps/core/teardown

          pip install --force-reinstall boto3

          echo Teardown for ${{ needs.detect-env.outputs.environment }}

          chmod a+x teardownStage.py
          python3 teardownStage.py ${{ steps.find-stage.outputs.stage }}
