name: ce-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: ce-api
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: ce
      part_name: api
      sonar_project_key: ${{ vars.SONAR_CE_API_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['ce-api'] }}

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: ce
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/core/ce/cqrs/cqrs-common
      use_eventbus_projection: true
      use_sqs_projection: false
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  deploy-infra:
    needs:
      - pre-deploy
      - deploy-cqrs
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-ce-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        task_name:
          - deploy:dynamoDb
          - deploy:api
          - deploy:sqs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          component_name: ce
          app_name: ce-api-serverless
          region: ${{ inputs.region }}
          task_name: ${{ matrix.task_name }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['ce-api'] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-apps:
    needs:
      - deploy-cqrs
      - deploy-infra
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-ce-api
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        task_name:
          - deploy:projection
          - deploy:ceQuery
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          component_name: ce
          app_name: ce-api-serverless
          region: ${{ inputs.region }}
          task_name: ${{ matrix.task_name }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['ce-api'] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-warmup:
    needs:
      - deploy-cqrs
      - deploy-apps
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-ce-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          component_name: ce
          app_name: ce-api-serverless
          region: ${{ inputs.region }}
          task_name: "deploy:warmup"
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['ce-api'] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  list-system-test-spec-files-matrix:
    runs-on: ubuntu-latest
    if: (inputs.environment == 'st'  && inputs.region == vars.SYDNEY_REGION ) || (inputs.environment == 'dev' && inputs.region == vars.SYDNEY_REGION)
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - deploy-warmup
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        run: |
          cd apps/core/ce/system-test/src
          echo "matrix=$(ls *.spec.ts | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

  ce-api-system-test:
    name: ce api system test
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-ce-api
    if: (inputs.environment == 'st'  && inputs.region == vars.SYDNEY_REGION ) || (inputs.environment == 'dev')
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - deploy-warmup
      - list-system-test-spec-files-matrix
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.matrix)}}
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['ce-api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage}}
        with:
          project-name: ${{inputs.environment}}-ce-api
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - set +e
                  - yarn install
                  - yarn nx build ce-api-system-test
                  - export STAGE=${{ inputs.stage }}
                  - yarn nx run ce-api-system-test:system:test src/${{ matrix.specFile}}
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            STAGE


  tag:
    runs-on: ubuntu-latest
    needs:
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - deploy-warmup
    environment: ${{ inputs.environment }}-ce-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-ce-api
          ghPat: ${{ secrets.GH_PAT }}
          region: ${{ inputs.region }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - tag
      - deploy-cqrs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: ce
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
