name: bff-device-api-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/device/api/**
      - .github/workflows/bff-device*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-bff-device-api:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component:
          - name: mp
            part: api
            app_name: mp-api
          - name: dbs
            part: api
            app_name: dbs-api
          - name: crms
            part: engine
            app_name: crms-engine
          - name: sdk
            part: api
            app_name: sdk-api
    environment: st-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ fromJson(vars.ROLE_TO_ASSUME)[matrix.component.app_name] }}
          stage: ${{ inputs.stage }}
          component_name: ${{ matrix.component.name }}
          part_name: ${{ matrix.component.part }}
          region: ${{ vars.SYDNEY_REGION }}
